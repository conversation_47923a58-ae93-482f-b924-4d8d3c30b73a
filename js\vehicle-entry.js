class VehicleEntry {
  constructor() {
    this.isExitMode = false
    this.currentVehicle = null
    this.keyboardEventBound = false
    this.debounceTimers = {}
    // 中国省份缩写数据
    this.provinces = [
      '京',
      '津',
      '冀',
      '晋',
      '蒙',
      '辽',
      '吉',
      '黑',
      '沪',
      '苏',
      '浙',
      '皖',
      '闽',
      '赣',
      '鲁',
      '豫',
      '鄂',
      '湘',
      '粤',
      '桂',
      '琼',
      '渝',
      '川',
      '贵',
      '云',
      '藏',
      '陕',
      '甘',
      '青',
      '宁',
      '新',
      '港',
      '澳',
      '台',
    ]
    // 字母选项
    this.letters = [
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'J',
      'K',
      'L',
      'M',
      'N',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z',
    ]
    this.init()
  }

  init() {
    this.checkModeFromUrl()
    this.setupEventListeners()
    this.loadParkingSelector()
    this.updateCurrentTime()
    this.loadRecentRecords()
    this.startTimeUpdates()
  }

  checkModeFromUrl() {
    const urlParams = new URLSearchParams(window.location.search)
    const action = urlParams.get('action')

    if (action === 'exit') {
      this.switchToExitMode()
    }
  }

  setupEventListeners() {
    // 模式切换按钮
    document.getElementById('switchModeBtn').addEventListener('click', () => {
      this.toggleMode()
    })

    // 表单提交
    document.getElementById('vehicleForm').addEventListener('submit', e => {
      e.preventDefault()
      this.handleSubmit()
    })

    // 车牌分段输入事件监听
    this.setupLicensePlateListeners()

    // 监听停车场切换事件
    window.addEventListener('parkingLotChanged', () => {
      this.loadParkingSelector()
    })

    // 监听页面可见性变化，优化性能
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，立即更新时间
        this.updateCurrentTime()
      }
    })
  }

  setupLicensePlateListeners() {
    const provincePart = document.getElementById('provincePart')
    const letterPart = document.getElementById('letterPart')
    const numberPart = document.getElementById('numberPart')

    // 为显示区域添加点击事件，激活虚拟键盘
    ;[provincePart, letterPart, numberPart].forEach(element => {
      element.addEventListener('click', () => {
        this.activateInputArea(element)
        this.showVirtualKeyboard()
      })
    })

    // 设置虚拟键盘监听器
    this.setupVirtualKeyboard()

    // 初始化显示区域状态
    this.initializeLicensePlateDisplay()
  }

  initializeLicensePlateDisplay() {
    // 初始化当前活动区域
    this.currentActiveArea = null

    // 重置所有显示区域
    this.resetLicensePlateDisplay()
  }

  activateInputArea(element) {
    // 使用requestAnimationFrame优化性能
    requestAnimationFrame(() => {
      // 移除之前的活动状态
      if (this.currentActiveArea) {
        this.currentActiveArea.classList.remove('active')
      }

      // 设置新的活动区域
      this.currentActiveArea = element
      element.classList.add('active')

      // 强制重绘以确保动画流畅
      element.offsetHeight
    })
  }

  showVirtualKeyboard() {
    const virtualKeyboard = document.getElementById('virtualKeyboard')
    const keyboardOverlay = document.getElementById('keyboardOverlay')

    // 防止重复显示
    if (!virtualKeyboard.classList.contains('hidden')) {
      return
    }

    // 使用requestAnimationFrame确保流畅的动画
    requestAnimationFrame(() => {
      keyboardOverlay.classList.remove('hidden')
      virtualKeyboard.classList.remove('hidden')

      // 初始化预览区域
      this.updateKeyboardPreview()

      // 强制重绘以确保动画流畅
      virtualKeyboard.offsetHeight
    })
  }

  hideVirtualKeyboard() {
    const virtualKeyboard = document.getElementById('virtualKeyboard')
    const keyboardOverlay = document.getElementById('keyboardOverlay')

    // 防止重复隐藏
    if (virtualKeyboard.classList.contains('hidden')) {
      return
    }

    // 使用requestAnimationFrame确保流畅的动画
    requestAnimationFrame(() => {
      keyboardOverlay.classList.add('hidden')
      virtualKeyboard.classList.add('hidden')

      // 重置预览区域
      const preview = document.getElementById('keyboardPreview')
      if (preview) {
        preview.textContent = '请输入车牌号'
        preview.classList.remove('has-value')
      }

      // 移除活动状态
      if (this.currentActiveArea) {
        this.currentActiveArea.classList.remove('active')
        this.currentActiveArea = null
      }
    })
  }

  resetLicensePlateDisplay() {
    const provincePart = document.getElementById('provincePart')
    const letterPart = document.getElementById('letterPart')
    const numberPart = document.getElementById('numberPart')

    // 重置显示内容
    provincePart.textContent = provincePart.getAttribute('data-placeholder')
    letterPart.textContent = letterPart.getAttribute('data-placeholder')
    numberPart.textContent = numberPart.getAttribute('data-placeholder')

    // 移除所有状态类
    ;[provincePart, letterPart, numberPart].forEach(element => {
      element.classList.remove('filled', 'error', 'active')
    })
  }

  setupVirtualKeyboard() {
    const keyboardToggle = document.getElementById('keyboardToggle')
    const virtualKeyboard = document.getElementById('virtualKeyboard')
    const keyboardOverlay = document.getElementById('keyboardOverlay')

    // 防止重复绑定事件
    if (this.keyboardEventBound) {
      return
    }

    // 切换键盘显示 - 使用防抖
    keyboardToggle.addEventListener(
      'click',
      this.debounce(() => {
        if (virtualKeyboard.classList.contains('hidden')) {
          this.showVirtualKeyboard()
        } else {
          this.hideVirtualKeyboard()
        }
      }, 100)
    )

    // 键盘按钮点击事件
    virtualKeyboard.addEventListener('click', e => {
      if (e.target.classList.contains('key-btn')) {
        const value = e.target.getAttribute('data-value')
        const action = e.target.getAttribute('data-action')

        if (action) {
          this.handleKeyboardAction(action)
        } else if (value) {
          this.handleKeyboardInput(value)
        }
      }
    })

    // 点击遮罩层关闭键盘
    keyboardOverlay.addEventListener('click', () => {
      this.hideVirtualKeyboard()
    })

    // 点击页面其他区域关闭键盘（使用防抖）
    document.addEventListener(
      'click',
      this.debounce(e => {
        const provincePart = document.getElementById('provincePart')
        const letterPart = document.getElementById('letterPart')
        const numberPart = document.getElementById('numberPart')

        if (
          !virtualKeyboard.contains(e.target) &&
          !keyboardToggle.contains(e.target) &&
          !keyboardOverlay.contains(e.target) &&
          !provincePart.contains(e.target) &&
          !letterPart.contains(e.target) &&
          !numberPart.contains(e.target) &&
          !virtualKeyboard.classList.contains('hidden')
        ) {
          this.hideVirtualKeyboard()
        }
      }, 150)
    )

    // 阻止键盘内部点击事件冒泡
    virtualKeyboard.addEventListener('click', e => {
      e.stopPropagation()
    })

    // 标记事件已绑定
    this.keyboardEventBound = true
  }

  handleKeyboardInput(value) {
    // 使用requestAnimationFrame优化性能
    requestAnimationFrame(() => {
      const provincePart = document.getElementById('provincePart')
      const letterPart = document.getElementById('letterPart')
      const numberPart = document.getElementById('numberPart')

      // 判断输入类型并插入到对应位置
      if (this.provinces.includes(value)) {
        // 省份输入
        provincePart.textContent = value
        provincePart.classList.add('filled')
        provincePart.classList.remove('error')

        // 自动切换到字母输入
        this.activateInputArea(letterPart)
      } else if (this.letters.includes(value) && this.currentActiveArea !== numberPart) {
        // 字母输入
        letterPart.textContent = value
        letterPart.classList.add('filled')
        letterPart.classList.remove('error')

        // 自动切换到数字输入
        this.activateInputArea(numberPart)
      } else if (/[0-9A-Z]/.test(value)) {
        // 字母数字输入
        const currentText = numberPart.textContent
        const placeholder = numberPart.getAttribute('data-placeholder')

        // 允许输入字母和数字
        if (currentText === placeholder) {
          numberPart.textContent = value
        } else if (currentText.length < 5) {
          // 修改为5位，因为车牌后五位
          numberPart.textContent += value
        }

        numberPart.classList.add('filled')
        numberPart.classList.remove('error')
      }

      this.updateFullLicensePlate()
    })
  }

  handleKeyboardAction(action) {
    const provincePart = document.getElementById('provincePart')
    const letterPart = document.getElementById('letterPart')
    const numberPart = document.getElementById('numberPart')

    switch (action) {
      case 'backspace':
        // 退格键 - 从当前活动区域或最后填写的区域开始删除
        if (
          this.currentActiveArea === numberPart ||
          (!this.currentActiveArea && numberPart.classList.contains('filled'))
        ) {
          const currentText = numberPart.textContent
          const placeholder = numberPart.getAttribute('data-placeholder')

          if (currentText !== placeholder && currentText.length > 0) {
            if (currentText.length === 1) {
              numberPart.textContent = placeholder
              numberPart.classList.remove('filled')
            } else {
              numberPart.textContent = currentText.slice(0, -1)
            }
          } else {
            // 字母数字部分为空，删除字母部分
            this.activateInputArea(letterPart)
            letterPart.textContent = letterPart.getAttribute('data-placeholder')
            letterPart.classList.remove('filled')
          }
        } else if (
          this.currentActiveArea === letterPart ||
          (!this.currentActiveArea && letterPart.classList.contains('filled'))
        ) {
          letterPart.textContent = letterPart.getAttribute('data-placeholder')
          letterPart.classList.remove('filled')
          this.activateInputArea(provincePart)
        } else if (
          this.currentActiveArea === provincePart ||
          (!this.currentActiveArea && provincePart.classList.contains('filled'))
        ) {
          provincePart.textContent = provincePart.getAttribute('data-placeholder')
          provincePart.classList.remove('filled')
        }
        this.updateFullLicensePlate()
        break

      case 'clear':
        // 清空
        this.clearLicensePlate()
        break

      case 'done':
        // 完成，关闭键盘
        this.hideVirtualKeyboard()
        if (this.isExitMode) {
          this.searchVehicle()
        }
        break
    }
  }

  updateFullLicensePlate() {
    const provincePart = document.getElementById('provincePart')
    const letterPart = document.getElementById('letterPart')
    const numberPart = document.getElementById('numberPart')

    const province =
      provincePart.textContent === provincePart.getAttribute('data-placeholder')
        ? ''
        : provincePart.textContent
    const letter =
      letterPart.textContent === letterPart.getAttribute('data-placeholder')
        ? ''
        : letterPart.textContent
    const number =
      numberPart.textContent === numberPart.getAttribute('data-placeholder')
        ? ''
        : numberPart.textContent

    const fullPlate = province + letter + number
    document.getElementById('licensePlate').value = fullPlate.toUpperCase()

    // 更新键盘预览
    this.updateKeyboardPreview()

    // 可视化验证反馈
    this.validateLicensePlateFormat()
  }

  validateLicensePlateFormat() {
    const provincePart = document.getElementById('provincePart')
    const letterPart = document.getElementById('letterPart')
    const numberPart = document.getElementById('numberPart')

    const province =
      provincePart.textContent === provincePart.getAttribute('data-placeholder')
        ? ''
        : provincePart.textContent
    const letter =
      letterPart.textContent === letterPart.getAttribute('data-placeholder')
        ? ''
        : letterPart.textContent
    const number =
      numberPart.textContent === numberPart.getAttribute('data-placeholder')
        ? ''
        : numberPart.textContent

    const elements = [provincePart, letterPart, numberPart]

    // 移除之前的验证样式
    elements.forEach(el => {
      el.classList.remove('error')
    })

    // 省份验证
    if (province && this.provinces.includes(province)) {
      // 省份正确，保持filled状态
    } else if (province) {
      provincePart.classList.add('error')
    }

    // 字母验证
    if (letter && this.letters.includes(letter)) {
      // 字母正确，保持filled状态
    } else if (letter) {
      letterPart.classList.add('error')
    }

    // 字母数字部分验证（简单长度检查）
    if (number && number.length >= 5) {
      // 字母数字长度正确，保持filled状态
    } else if (number) {
      numberPart.classList.add('error')
    }
  }

  setLicensePlateFromFull(fullPlate) {
    if (!fullPlate || fullPlate.length < 2) return

    const province = fullPlate.charAt(0)
    const letter = fullPlate.charAt(1)
    const number = fullPlate.substring(2)

    const provincePart = document.getElementById('provincePart')
    const letterPart = document.getElementById('letterPart')
    const numberPart = document.getElementById('numberPart')

    provincePart.textContent = province
    provincePart.classList.add('filled')

    letterPart.textContent = letter
    letterPart.classList.add('filled')

    numberPart.textContent = number
    numberPart.classList.add('filled')

    this.updateFullLicensePlate()
  }

  updateKeyboardPreview() {
    const preview = document.getElementById('keyboardPreview')
    if (!preview) return

    const provincePart = document.getElementById('provincePart')
    const letterPart = document.getElementById('letterPart')
    const numberPart = document.getElementById('numberPart')

    const province =
      provincePart.textContent === provincePart.getAttribute('data-placeholder')
        ? ''
        : provincePart.textContent
    const letter =
      letterPart.textContent === letterPart.getAttribute('data-placeholder')
        ? ''
        : letterPart.textContent
    const number =
      numberPart.textContent === numberPart.getAttribute('data-placeholder')
        ? ''
        : numberPart.textContent

    const fullPlate = province + letter + number

    if (fullPlate) {
      preview.textContent = fullPlate.toUpperCase()
      preview.classList.add('has-value')
    } else {
      preview.textContent = '请输入车牌号'
      preview.classList.remove('has-value')
    }
  }

  clearLicensePlate() {
    this.resetLicensePlateDisplay()
    document.getElementById('licensePlate').value = ''

    // 更新键盘预览
    this.updateKeyboardPreview()

    // 重置当前活动区域
    if (this.currentActiveArea) {
      this.currentActiveArea.classList.remove('active')
      this.currentActiveArea = null
    }
  }

  toggleMode() {
    if (this.isExitMode) {
      this.switchToEntryMode()
    } else {
      this.switchToExitMode()
    }
  }

  switchToEntryMode() {
    this.isExitMode = false
    this.currentVehicle = null

    document.getElementById('pageTitle').textContent = '车辆入场'
    document.getElementById('pageSubtitle').textContent = '记录车辆进入停车场'
    document.getElementById('formTitle').textContent = '车辆入场登记'
    document.getElementById('submitText').textContent = '确认入场'
    document.getElementById('switchModeBtn').textContent = '切换到出场'
    document.getElementById('exitSection').classList.add('hidden')
    document.getElementById('entryTimeField').classList.remove('hidden')

    this.clearLicensePlate()
    // 激活省份输入区域
    this.activateInputArea(document.getElementById('provincePart'))
  }

  switchToExitMode() {
    this.isExitMode = true
    this.currentVehicle = null

    document.getElementById('pageTitle').textContent = '车辆出场'
    document.getElementById('pageSubtitle').textContent = '处理车辆离开并计算费用'
    document.getElementById('formTitle').textContent = '车辆出场结算'
    document.getElementById('submitText').textContent = '确认出场并收费'
    document.getElementById('switchModeBtn').textContent = '切换到入场'
    document.getElementById('exitSection').classList.remove('hidden')
    document.getElementById('entryTimeField').classList.add('hidden')

    this.clearCalculation()
    // 激活省份输入区域
    this.activateInputArea(document.getElementById('provincePart'))
  }

  loadParkingSelector() {
    const container = document.getElementById('parkingSelector')
    const parkingLots = storage.getParkingLots()

    if (parkingLots.length === 0) {
      container.innerHTML = `
                <div class="text-red-400 text-sm">
                    暂无停车场，请先<a href="parking-management.html" class="underline">添加停车场</a>
                </div>
            `
      return
    }

    let html = '<select class="input-glass w-full" id="parkingSelect" required>'
    html += '<option value="">选择停车场</option>'

    const currentParkingId = storage.getCurrentParkingLotId()

    parkingLots.forEach(parking => {
      const stats = storage.getParkingLotStats(parking.id)
      const availableText = stats ? ` (空闲: ${stats.availableSpaces})` : ''
      const selected = parking.id === currentParkingId ? 'selected' : ''
      html += `<option value="${parking.id}" ${selected}>${parking.name}${availableText}</option>`
    })

    html += '</select>'
    container.innerHTML = html
  }

  updateCurrentTime() {
    const now = new Date()
    const timeString = now.toLocaleTimeString('zh-CN')

    document.getElementById('currentTime').textContent = timeString
    document.getElementById('exitTime').textContent = timeString

    if (this.currentVehicle) {
      this.calculateFee()
    }
  }

  startTimeUpdates() {
    // 优化定时器性能，减少不必要的更新
    this.timeUpdateInterval = setInterval(() => {
      // 只有在页面可见时才更新时间
      if (document.visibilityState === 'visible') {
        this.updateCurrentTime()
      }
    }, 1000)
  }

  searchVehicle() {
    const licensePlate = document.getElementById('licensePlate').value.trim().toUpperCase()

    if (!licensePlate) {
      componentLoader.showToast('请输入车牌号码', 'warning')
      return
    }

    const vehicles = storage.getVehicles()
    const parkedVehicle = vehicles.find(
      v => v.licensePlate === licensePlate && v.status === 'parked'
    )

    if (!parkedVehicle) {
      componentLoader.showToast('未找到该车辆的入场记录', 'error')
      this.clearCalculation()
      return
    }

    this.currentVehicle = parkedVehicle

    // 设置车牌号分段显示
    this.setLicensePlateFromFull(parkedVehicle.licensePlate)

    // 显示入场时间
    const entryTime = new Date(parkedVehicle.entryTime)
    document.getElementById('entryTimeDisplay').textContent = entryTime.toLocaleString('zh-CN')

    // 设置停车场选择
    const parkingSelect = document.getElementById('parkingSelect')
    if (parkingSelect) {
      parkingSelect.value = parkedVehicle.parkingLotId
      parkingSelect.disabled = true
    }

    // 计算费用
    this.calculateFee()

    componentLoader.showToast('找到车辆记录，开始计费', 'success')
  }

  calculateFee() {
    if (!this.currentVehicle) return

    const entryTime = new Date(this.currentVehicle.entryTime)
    const exitTime = new Date()

    // 获取停车场费率
    const parkingLots = storage.getParkingLots()
    const parking = parkingLots.find(p => p.id === this.currentVehicle.parkingLotId)
    const hourlyRate = parking ? parking.hourlyRate : 5

    // 计算费用
    const amount = storage.calculateParkingFee(entryTime, exitTime, hourlyRate)

    // 计算停车时长
    const durationMs = exitTime - entryTime
    const hours = Math.floor(durationMs / (1000 * 60 * 60))
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60))

    // 更新显示
    document.getElementById('parkingDuration').textContent = `${hours}小时${minutes}分钟`
    document.getElementById('hourlyRate').textContent = `¥${hourlyRate.toFixed(2)}/小时`
    document.getElementById('totalAmount').textContent = `¥${amount.toFixed(2)}`
  }

  clearCalculation() {
    document.getElementById('entryTimeDisplay').textContent = '--:--:--'
    document.getElementById('parkingDuration').textContent = '0小时0分钟'
    document.getElementById('hourlyRate').textContent = '¥0.00/小时'
    document.getElementById('totalAmount').textContent = '¥0.00'

    const parkingSelect = document.getElementById('parkingSelect')
    if (parkingSelect) {
      parkingSelect.disabled = false
      parkingSelect.value = ''
    }
  }

  async handleSubmit() {
    const licensePlate = document.getElementById('licensePlate').value.trim().toUpperCase()
    const parkingSelect = document.getElementById('parkingSelect')

    if (!licensePlate) {
      componentLoader.showToast('请输入完整的车牌号码', 'error')
      return
    }

    // 基本车牌格式验证
    if (licensePlate.length < 7) {
      componentLoader.showToast('车牌号码长度不足', 'error')
      return
    }

    if (!parkingSelect.value && !this.isExitMode) {
      componentLoader.showToast('请选择停车场', 'error')
      return
    }

    try {
      if (this.isExitMode) {
        await this.processExit(licensePlate)
      } else {
        await this.processEntry(licensePlate, parkingSelect.value)
      }
    } catch (error) {
      console.error('处理失败:', error)
      componentLoader.showToast('操作失败，请重试', 'error')
    }
  }

  async processEntry(licensePlate, parkingLotId) {
    // 检查车辆是否已经入场
    const vehicles = storage.getVehicles()
    const existingVehicle = vehicles.find(
      v => v.licensePlate === licensePlate && v.status === 'parked'
    )

    if (existingVehicle) {
      componentLoader.showToast('该车辆已在停车场内', 'error')
      return
    }

    // 检查停车场是否有空位
    const stats = storage.getParkingLotStats(parkingLotId)
    if (stats && stats.availableSpaces <= 0) {
      componentLoader.showToast('该停车场已满', 'error')
      return
    }

    // 添加车辆记录
    const vehicle = storage.addVehicle({
      licensePlate: licensePlate,
      parkingLotId: parkingLotId,
    })

    componentLoader.showToast(`车辆 ${licensePlate} 入场成功`, 'success')

    // 清空表单
    this.clearLicensePlate()
    parkingSelect.value = ''
    // 激活省份输入区域
    this.activateInputArea(document.getElementById('provincePart'))

    // 刷新最近记录
    this.loadRecentRecords()
  }

  async processExit(licensePlate) {
    if (!this.currentVehicle) {
      componentLoader.showToast('请先搜索车辆', 'error')
      return
    }

    const exitTime = new Date()
    const amount = parseFloat(document.getElementById('totalAmount').textContent.replace('¥', ''))

    // 更新车辆状态
    storage.updateVehicle(this.currentVehicle.id, {
      exitTime: exitTime.toISOString(),
      status: 'exited',
    })

    // 添加交易记录
    storage.addTransaction({
      licensePlate: this.currentVehicle.licensePlate,
      parkingLotId: this.currentVehicle.parkingLotId,
      entryTime: this.currentVehicle.entryTime,
      exitTime: exitTime.toISOString(),
      amount: amount,
      status: 'completed',
    })

    componentLoader.showToast(
      `车辆 ${licensePlate} 出场成功，收费 ¥${amount.toFixed(2)}`,
      'success'
    )

    // 清空表单
    this.clearCalculation()
    this.clearLicensePlate()
    this.currentVehicle = null

    const parkingSelect = document.getElementById('parkingSelect')
    if (parkingSelect) {
      parkingSelect.disabled = false
      parkingSelect.value = ''
    }

    // 刷新最近记录
    this.loadRecentRecords()
  }

  loadRecentRecords() {
    const container = document.getElementById('recentRecords')
    const vehicles = storage.getVehicles().slice(0, 10) // 最新10条记录

    if (vehicles.length === 0) {
      container.innerHTML = '<div class="text-center py-4 text-gray-500">暂无记录</div>'
      return
    }

    let html = ''
    vehicles.forEach(vehicle => {
      const entryTime = new Date(vehicle.entryTime)
      const status = vehicle.status === 'parked' ? '🟢 停放中' : '🔴 已出场'
      const timeText = entryTime.toLocaleString('zh-CN')

      html += `
                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <span class="text-2xl">🚗</span>
                        <div>
                            <div class="font-mono text-sm">${vehicle.licensePlate}</div>
                            <div class="text-xs text-gray-400">${timeText}</div>
                        </div>
                    </div>
                    <span class="text-sm ${
                      vehicle.status === 'parked' ? 'text-green-400' : 'text-gray-400'
                    }">
                        ${status}
                    </span>
                </div>
            `
    })

    container.innerHTML = html
  }

  // 防抖函数
  debounce(func, wait) {
    const key = func.toString()
    return (...args) => {
      clearTimeout(this.debounceTimers[key])
      this.debounceTimers[key] = setTimeout(() => {
        func.apply(this, args)
      }, wait)
    }
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
  // 组件加载完成后初始化
  setTimeout(() => {
    // 初始化车辆入场管理器
    window.vehicleEntry = new VehicleEntry()

    // 如果停车场切换器存在，重新初始化它
    if (
      typeof ParkingSwitcher !== 'undefined' &&
      typeof storage !== 'undefined' &&
      typeof componentLoader !== 'undefined'
    ) {
      if (!window.parkingSwitcher) {
        window.parkingSwitcher = new ParkingSwitcher()
      }
    }
  }, 100)
})
