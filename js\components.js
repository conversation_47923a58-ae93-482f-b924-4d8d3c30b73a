// 组件加载器
class ComponentLoader {
  constructor() {
    this.components = {}
  }

  async loadComponent(componentName, targetElementId) {
    return new Promise((resolve, reject) => {
      // 临时使用内联内容而不是动态加载
      const inlineContents = {
        header: `
<header class="fixed top-0 left-0 right-0 bg-gray-900/80 backdrop-blur-md border-b border-gray-700 z-50">
    <div class="flex items-center justify-between px-6 py-4">
        <div class="flex items-center space-x-4">
            <button id="sidebarToggle" class="lg:hidden text-gray-300 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
            <h1 class="text-xl font-bold text-white">
                <span class="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    智慧停车场管理系统
                </span>
            </h1>
        </div>
        
        <div class="flex items-center space-x-4">
            <!-- 停车场切换器 -->
            <div class="relative" id="parkingSwitcher">
                <button class="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <span class="text-sm text-gray-300" id="currentParkingName">选择停车场</span>
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                
                <div class="absolute right-0 mt-2 w-64 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 transform scale-95" id="parkingDropdown" style="z-index: 9999;">
                    <div class="py-2 max-h-60 overflow-y-auto" id="parkingListDropdown">
                        <!-- 停车场列表将通过JS动态生成 -->
                    </div>
                </div>
            </div>
            
            <div class="hidden md:flex items-center space-x-2 text-sm text-gray-300">
                <div class="status-indicator status-active"></div>
                <span>系统运行中</span>
            </div>
            
            <div class="relative">
                <button class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </button>
                
                <div class="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 transform scale-95" style="z-index: 9999;">
                    <div class="py-2">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">个人资料</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">系统设置</a>
                        <div class="border-t border-gray-700 my-1"></div>
                        <a href="#" class="block px-4 py-2 text-sm text-red-400 hover:bg-gray-700">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

      `,
        sidebar: `
<aside
  id="sidebar"
  class="fixed left-0 top-0 h-full bg-gray-900/80 backdrop-blur-md border-r border-gray-700 w-64 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 z-40 pt-16"
>
  <nav class="p-4">
    <div class="mb-8">
      <div class="flex items-center space-x-3 p-3 bg-gray-800/50 rounded-lg">
        <div
          class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
        >
          <span class="text-white font-bold text-lg">🅿️</span>
        </div>
        <div>
          <p class="text-sm text-gray-300">当前停车场</p>
          <p class="font-semibold text-white" id="currentParking">未选择</p>
        </div>
      </div>
    </div>

    <ul class="space-y-2">
      <li>
        <a
          href="index.html"
          class="flex items-center space-x-3 p-3 text-blue-400 bg-blue-500/10 rounded-lg"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zm10 0a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
            ></path>
          </svg>
          <span>控制台</span>
        </a>
      </li>

      <li>
        <a
          href="parking-management.html"
          class="flex items-center space-x-3 p-3 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m4 0H9m4 0V9a2 2 0 00-2-2H5a2 2 0 00-2 2v10m4 0h4m-4 0H3m4 0v4"
            ></path>
          </svg>
          <span>停车场管理</span>
        </a>
      </li>

      <li>
        <a
          href="vehicle-entry.html"
          class="flex items-center space-x-3 p-3 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <span>车辆进出</span>
        </a>
      </li>

      <li>
        <a
          href="parking-status.html"
          class="flex items-center space-x-3 p-3 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
            ></path>
          </svg>
          <span>车位状态</span>
        </a>
      </li>

      <li>
        <a
          href="billing-records.html"
          class="flex items-center space-x-3 p-3 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          <span>收费记录</span>
        </a>
      </li>

      <li>
        <a
          href="reports.html"
          class="flex items-center space-x-3 p-3 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            ></path>
          </svg>
          <span>统计报表</span>
        </a>
      </li>
    </ul>

    <div class="mt-8 pt-4 border-t border-gray-700">
      <ul class="space-y-2">
        <li>
          <a
            href="settings.html"
            class="flex items-center space-x-3 p-3 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
              ></path>
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              ></path>
            </svg>
            <span>系统设置</span>
          </a>
        </li>

        <li>
          <a
            href="help.html"
            class="flex items-center space-x-3 p-3 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
            <span>帮助中心</span>
          </a>
        </li>
      </ul>
    </div>
  </nav>
</aside>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const sidebarToggle = document.getElementById('sidebarToggle')
    const sidebar = document.getElementById('sidebar')

    if (sidebarToggle && sidebar) {
      sidebarToggle.addEventListener('click', function () {
        sidebar.classList.toggle('-translate-x-full')
      })
    }

    // 更新当前停车场信息
    updateCurrentParking()
  })

  function updateCurrentParking() {
    // 检查 storage 对象是否已加载
    if (typeof storage === 'undefined' || !storage.getParkingLots) {
      console.warn('storage 对象尚未加载，稍后重试')
      setTimeout(updateCurrentParking, 100) // 100ms后重试
      return
    }
    
    const parkingLots = storage.getParkingLots()
    const currentParkingElement = document.getElementById('currentParking')

    if (parkingLots.length > 0 && currentParkingElement) {
      // 默认显示第一个停车场
      currentParkingElement.textContent = parkingLots[0].name
    }
  }
</script>
      `,
      }

      const targetElement = document.getElementById(targetElementId)

      if (targetElement && inlineContents[componentName]) {
        targetElement.innerHTML = inlineContents[componentName]
        this.executeScripts(targetElement)
        this.components[componentName] = inlineContents[componentName]

        // 等待DOM更新完成
        setTimeout(() => {
          console.log(`组件 ${componentName} 加载完成`)
          resolve()
        }, 100)
      } else {
        console.error(`🔍 无法加载组件: ${componentName}`)
        this.showError(`无法加载 ${componentName} 组件`)
        reject(new Error(`无法加载 ${componentName} 组件`))
      }
    })
  }

  executeScripts(container) {
    const scripts = container.querySelectorAll('script')

    scripts.forEach((script, index) => {
      try {
        if (script.src) {
          // 对于外部脚本，创建新的script元素
          const newScript = document.createElement('script')
          newScript.src = script.src

          // 添加错误处理
          newScript.onerror = error => {
            console.error('🔍 组件脚本执行失败:', error)
            componentLoader.showError('组件脚本执行失败')
          }

          // 添加加载完成处理
          newScript.onload = () => {
            console.log('🔍 外部脚本加载成功:', script.src)
          }

          document.head.appendChild(newScript)
        } else {
          // 对于内联脚本，只使用eval执行一次，避免重复执行
          try {
            eval(script.textContent)
            console.log('🔍 内联脚本执行成功')
          } catch (evalError) {
            console.error('🔍 内联脚本执行错误:', evalError)
            throw evalError
          }
        }
      } catch (error) {
        console.error('🔍 脚本处理失败:', error)
      }
    })
  }

  showError(message) {
    // 创建错误提示
    const errorDiv = document.createElement('div')
    errorDiv.className =
      'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50'
    errorDiv.textContent = message

    document.body.appendChild(errorDiv)

    // 3秒后自动消失
    setTimeout(() => {
      errorDiv.remove()
    }, 3000)
  }

  // 批量加载组件
  async loadAllComponents() {
    const componentsToLoad = [
      { name: 'header', target: 'header' },
      { name: 'sidebar', target: 'sidebar' },
    ]

    const loadPromises = componentsToLoad.map(comp => this.loadComponent(comp.name, comp.target))

    await Promise.all(loadPromises)
  }

  // 获取组件HTML
  getComponent(componentName) {
    return this.components[componentName]
  }

  // 动态创建停车场选择器
  createParkingSelector(currentParkingLotId = null) {
    const parkingLots = storage.getParkingLots()

    if (parkingLots.length === 0) {
      return '<div class="text-gray-400 text-sm">暂无停车场</div>'
    }

    let html = '<select class="input-glass w-full">'
    parkingLots.forEach(parking => {
      const selected = parking.id === currentParkingLotId ? 'selected' : ''
      html += `<option value="${parking.id}" ${selected}>${parking.name}</option>`
    })
    html += '</select>'

    return html
  }

  // 创建状态指示器
  createStatusIndicator(status, text) {
    const statusClass =
      {
        active: 'status-active',
        inactive: 'status-inactive',
        warning: 'status-warning',
      }[status] || 'status-inactive'

    return `
            <div class="flex items-center">
                <span class="status-indicator ${statusClass}"></span>
                <span class="text-sm">${text}</span>
            </div>
        `
  }

  // 创建加载动画
  createLoadingSpinner() {
    return '<div class="loading-spinner"></div>'
  }

  // 创建 toast 通知
  showToast(message, type = 'info') {
    const toast = document.createElement('div')
    const bgColor =
      {
        success: 'bg-green-500',
        error: 'bg-red-500',
        warning: 'bg-yellow-500',
        info: 'bg-blue-500',
      }[type] || 'bg-blue-500'

    toast.className = `fixed top-4 right-4 ${bgColor} text-white px-4 py-2 rounded-lg shadow-lg z-50 transform transition-all duration-300 translate-x-full`
    toast.textContent = message

    document.body.appendChild(toast)

    // 显示动画
    setTimeout(() => {
      toast.classList.remove('translate-x-full')
      toast.classList.add('translate-x-0')
    }, 100)

    // 3秒后消失
    setTimeout(() => {
      toast.classList.remove('translate-x-0')
      toast.classList.add('translate-x-full')
      setTimeout(() => toast.remove(), 300)
    }, 3000)
  }
}

// 全局组件加载器实例
const componentLoader = new ComponentLoader()

// ParkingSwitcher 类 - 管理顶部停车场切换器
class ParkingSwitcher {
  constructor() {
    this.init()
  }

  init() {
    console.log('ParkingSwitcher 初始化开始')

    // 确保DOM元素存在
    const container = document.getElementById('parkingListDropdown')
    const nameElement = document.getElementById('currentParkingName')

    if (!container || !nameElement) {
      console.error('停车场切换器必需的DOM元素未找到')
      setTimeout(() => this.init(), 200)
      return
    }

    this.loadCurrentParking()
    this.renderParkingList()
    this.setupEventListeners()
    console.log('ParkingSwitcher 初始化完成')
  }

  loadCurrentParking() {
    // 确保storage已经初始化
    if (typeof storage === 'undefined' || !storage.getCurrentParkingLot) {
      console.warn('storage对象尚未初始化，延迟加载当前停车场')
      setTimeout(() => this.loadCurrentParking(), 100)
      return
    }

    const currentParking = storage.getCurrentParkingLot()
    const nameElement = document.getElementById('currentParkingName')

    if (currentParking && nameElement) {
      const stats = storage.getParkingLotStats(currentParking.id)
      const statusText = stats ? stats.availableSpaces + '空位' : '加载中'
      nameElement.textContent = currentParking.name + ' (' + statusText + ')'
      nameElement.classList.remove('text-gray-300')
      nameElement.classList.add('text-white', 'font-medium')
      console.log('已加载当前停车场:', currentParking.name)
    } else if (nameElement) {
      nameElement.textContent = '选择停车场'
      nameElement.classList.remove('text-white', 'font-medium')
      nameElement.classList.add('text-gray-300')
      console.log('未找到当前停车场，显示默认文本')
    }
  }

  renderParkingList() {
    const container = document.getElementById('parkingListDropdown')
    if (!container) {
      console.warn('停车场下拉容器未找到')
      return
    }

    // 确保storage已经初始化
    if (typeof storage === 'undefined' || !storage.getParkingLots) {
      console.warn('storage对象尚未初始化，延迟渲染停车场列表')
      setTimeout(() => this.renderParkingList(), 100)
      return
    }

    const parkingLots = storage.getParkingLots()
    console.log('获取到停车场数据:', parkingLots)

    if (parkingLots.length === 0) {
      container.innerHTML =
        '<div class="px-4 py-3 text-sm text-gray-400 text-center">暂无停车场</div>'
      return
    }

    // 清空容器并使用DOM方法创建元素
    container.innerHTML = ''
    const currentId = storage.getCurrentParkingLotId()

    parkingLots.forEach(parking => {
      const stats = storage.getParkingLotStats(parking.id)
      const isCurrent = parking.id === currentId
      const statusColor = this.getStatusColor(stats ? stats.usageRate : 0)

      const availableText = stats
        ? stats.availableSpaces + '/' + stats.totalSpaces + '空位'
        : '加载中'
      const nameClass = isCurrent ? 'text-white font-medium' : 'text-gray-300'
      const usageRateText = stats ? stats.usageRate + '%' : '0%'
      const buttonClass = isCurrent ? 'bg-blue-600/20' : ''

      // 创建按钮元素
      const button = document.createElement('button')
      button.className =
        'w-full text-left px-4 py-3 hover:bg-gray-700 transition-colors ' + buttonClass
      button.setAttribute('data-parking-id', parking.id)

      // 使用事件监听器而不是onclick属性
      button.addEventListener('click', () => {
        this.switchParking(parking.id)
      })

      // 创建内容结构
      const topDiv = document.createElement('div')
      topDiv.className = 'flex items-center justify-between'

      const leftDiv = document.createElement('div')
      leftDiv.className = 'flex items-center space-x-3'

      const checkmarkSpan = document.createElement('span')
      if (isCurrent) {
        checkmarkSpan.className = 'text-blue-400'
        checkmarkSpan.textContent = '✓'
      } else {
        checkmarkSpan.className = 'w-4'
      }

      const nameSpan = document.createElement('span')
      nameSpan.className = 'text-sm ' + nameClass
      nameSpan.textContent = parking.name

      const usageSpan = document.createElement('span')
      usageSpan.className = 'text-xs px-2 py-1 rounded-full ' + statusColor
      usageSpan.textContent = usageRateText

      const bottomDiv = document.createElement('div')
      bottomDiv.className = 'text-xs text-gray-400 mt-1 ml-7'
      bottomDiv.textContent = (parking.location || '未设置位置') + ' • ' + availableText

      // 组装元素
      leftDiv.appendChild(checkmarkSpan)
      leftDiv.appendChild(nameSpan)
      topDiv.appendChild(leftDiv)
      topDiv.appendChild(usageSpan)
      button.appendChild(topDiv)
      button.appendChild(bottomDiv)
      container.appendChild(button)
    })
  }

  getStatusColor(usageRate) {
    if (usageRate >= 80) return 'bg-red-500/20 text-red-400'
    if (usageRate >= 50) return 'bg-yellow-500/20 text-yellow-400'
    return 'bg-green-500/20 text-green-400'
  }

  switchParking(parkingLotId) {
    storage.setCurrentParkingLotId(parkingLotId)
    this.loadCurrentParking()
    this.renderParkingList()

    const parking = storage.getParkingLots().find(p => p.id === parkingLotId)
    if (parking) {
      componentLoader.showToast('已切换到: ' + parking.name, 'success')
    }
  }

  setupEventListeners() {
    console.log('设置停车场切换器事件监听器')

    // 添加悬停功能
    const switcher = document.getElementById('parkingSwitcher')
    const dropdown = document.getElementById('parkingDropdown')

    console.log('DOM元素检查:', {
      switcher: !!switcher,
      dropdown: !!dropdown,
      switcherId: switcher ? switcher.id : 'null',
      dropdownId: dropdown ? dropdown.id : 'null',
    })

    if (switcher && dropdown) {
      console.log('绑定鼠标悬停事件')

      switcher.addEventListener('mouseenter', () => {
        console.log('鼠标进入停车场切换器')
        dropdown.classList.remove('opacity-0', 'invisible', 'scale-95')
        dropdown.classList.add('opacity-100', 'visible', 'scale-100')
        console.log('下拉菜单应该已显示，当前类名:', dropdown.className)
      })

      switcher.addEventListener('mouseleave', () => {
        console.log('鼠标离开停车场切换器')
        dropdown.classList.add('opacity-0', 'invisible', 'scale-95')
        dropdown.classList.remove('opacity-100', 'visible', 'scale-100')
        console.log('下拉菜单应该已隐藏，当前类名:', dropdown.className)
      })

      console.log('✅ 悬停事件监听器绑定完成')
    } else {
      console.error('❌ 无法绑定悬停事件，DOM元素缺失:', {
        switcher: !!switcher,
        dropdown: !!dropdown,
      })
    }

    // 监听停车场切换事件
    window.addEventListener('parkingLotChanged', event => {
      this.loadCurrentParking()
      this.renderParkingList()
    })

    // 监听storage变化
    window.addEventListener('storage', event => {
      if (event.key === 'parkingLots' || event.key === 'currentParkingLotId') {
        this.loadCurrentParking()
        this.renderParkingList()
      }
    })

    // 设置用户头像下拉菜单
    this.setupProfileDropdown()
  }

  setupProfileDropdown() {
    const profileButton = document.querySelector('header button:last-of-type')
    const dropdown = document.querySelector('header .absolute:last-of-type')

    if (profileButton && dropdown) {
      profileButton.addEventListener('click', function (e) {
        e.stopPropagation()
        dropdown.classList.toggle('opacity-0')
        dropdown.classList.toggle('invisible')
        dropdown.classList.toggle('scale-95')
        dropdown.classList.toggle('opacity-100')
        dropdown.classList.toggle('visible')
        dropdown.classList.toggle('scale-100')
      })

      // 点击外部关闭下拉菜单
      document.addEventListener('click', function (event) {
        if (!profileButton.contains(event.target) && !dropdown.contains(event.target)) {
          dropdown.classList.add('opacity-0', 'invisible', 'scale-95')
          dropdown.classList.remove('opacity-100', 'visible', 'scale-100')
        }
      })
    }
  }
}

// 页面加载时自动加载组件和初始化停车场切换器
document.addEventListener('DOMContentLoaded', async function () {
  console.log('DOMContentLoaded 事件触发')

  try {
    await componentLoader.loadAllComponents()
    console.log('所有组件加载完成')
  } catch (error) {
    console.error('组件加载失败:', error)
  }

  // 初始化停车场切换器 - 确保storage和DOM都准备好后再初始化
  function initParkingSwitcher() {
    // 检查必要的条件
    const storageReady = typeof storage !== 'undefined' && storage.getParkingLots
    const componentLoaderReady = typeof componentLoader !== 'undefined'
    const domReady =
      document.getElementById('parkingListDropdown') &&
      document.getElementById('currentParkingName')

    console.log('初始化检查:', {
      storageReady,
      componentLoaderReady,
      domReady,
      parkingSwitcherExists: !!window.parkingSwitcher,
    })

    if (storageReady && componentLoaderReady && domReady) {
      if (!window.parkingSwitcher) {
        console.log('开始初始化停车场切换器')
        try {
          window.parkingSwitcher = new ParkingSwitcher()
          console.log('停车场切换器初始化完成')
        } catch (error) {
          console.error('停车场切换器初始化失败:', error)
        }
      }
    } else {
      console.log('等待条件满足，重试初始化...')
      setTimeout(initParkingSwitcher, 200)
    }
  }

  // 延迟初始化以确保所有脚本和DOM都已加载
  setTimeout(initParkingSwitcher, 800)

  // 添加一个全局方法来强制刷新停车场数据
  window.refreshParkingSwitcher = function () {
    if (window.parkingSwitcher) {
      console.log('强制刷新停车场切换器')
      window.parkingSwitcher.loadCurrentParking()
      window.parkingSwitcher.renderParkingList()
    } else {
      console.log('ParkingSwitcher 不存在，尝试重新初始化')
      initParkingSwitcher()
    }
  }

  // 添加一个全局方法来手动修复下拉菜单
  window.fixParkingDropdown = function () {
    console.log('=== 手动修复停车场下拉菜单 ===')

    const switcher = document.getElementById('parkingSwitcher')
    const dropdown = document.getElementById('parkingDropdown')
    const dropdownList = document.getElementById('parkingListDropdown')
    const nameElement = document.getElementById('currentParkingName')

    console.log('DOM元素检查:', {
      switcher: !!switcher,
      dropdown: !!dropdown,
      dropdownList: !!dropdownList,
      nameElement: !!nameElement,
    })

    if (!switcher || !dropdown || !dropdownList || !nameElement) {
      console.error('❌ 关键DOM元素缺失，无法修复')
      return false
    }

    // 1. 重新渲染停车场列表
    if (typeof storage !== 'undefined' && storage.getParkingLots) {
      const parkingLots = storage.getParkingLots()
      const currentId = storage.getCurrentParkingLotId()

      console.log(`重新渲染 ${parkingLots.length} 个停车场`)

      // 清空列表
      dropdownList.innerHTML = ''

      if (parkingLots.length === 0) {
        dropdownList.innerHTML =
          '<div class="px-4 py-3 text-sm text-gray-400 text-center">暂无停车场</div>'
      } else {
        parkingLots.forEach(parking => {
          const isCurrent = parking.id === currentId

          const button = document.createElement('button')
          button.className = `w-full text-left px-4 py-3 hover:bg-gray-700 transition-colors ${
            isCurrent ? 'bg-blue-600/20' : ''
          }`

          const content = document.createElement('div')
          content.className = 'flex items-center justify-between'

          const leftDiv = document.createElement('div')
          leftDiv.className = 'flex items-center space-x-3'

          const checkmark = document.createElement('span')
          if (isCurrent) {
            checkmark.className = 'text-blue-400'
            checkmark.textContent = '✓'
          } else {
            checkmark.className = 'w-4'
          }

          const name = document.createElement('span')
          name.className = `text-sm ${isCurrent ? 'text-white font-medium' : 'text-gray-300'}`
          name.textContent = parking.name

          const location = document.createElement('div')
          location.className = 'text-xs text-gray-400 mt-1 ml-7'
          location.textContent = parking.location || '未设置位置'

          leftDiv.appendChild(checkmark)
          leftDiv.appendChild(name)
          content.appendChild(leftDiv)
          button.appendChild(content)
          button.appendChild(location)

          // 添加点击事件
          button.addEventListener('click', () => {
            console.log(`点击了停车场: ${parking.name}`)
            storage.setCurrentParkingLotId(parking.id)
            nameElement.textContent = parking.name
            // 隐藏下拉菜单
            dropdown.classList.add('opacity-0', 'invisible', 'scale-95')
            dropdown.classList.remove('opacity-100', 'visible', 'scale-100')
            // 重新渲染
            window.fixParkingDropdown()
          })

          dropdownList.appendChild(button)
        })

        // 更新当前停车场名称
        const currentParking = storage.getCurrentParkingLot()
        if (currentParking) {
          nameElement.textContent = currentParking.name
          nameElement.classList.remove('text-gray-300')
          nameElement.classList.add('text-white', 'font-medium')
        }
      }
    }

    // 2. 重新绑定事件监听器
    console.log('重新绑定悬停事件')

    // 移除旧的事件监听器
    const newSwitcher = switcher.cloneNode(true)
    switcher.parentNode.replaceChild(newSwitcher, switcher)

    // 重新获取元素并绑定事件
    const freshSwitcher = document.getElementById('parkingSwitcher')
    const freshDropdown = document.getElementById('parkingDropdown')

    if (freshSwitcher && freshDropdown) {
      freshSwitcher.addEventListener('mouseenter', () => {
        console.log('鼠标进入 - 显示下拉菜单')
        freshDropdown.classList.remove('opacity-0', 'invisible', 'scale-95')
        freshDropdown.classList.add('opacity-100', 'visible', 'scale-100')
      })

      freshSwitcher.addEventListener('mouseleave', () => {
        console.log('鼠标离开 - 隐藏下拉菜单')
        freshDropdown.classList.add('opacity-0', 'invisible', 'scale-95')
        freshDropdown.classList.remove('opacity-100', 'visible', 'scale-100')
      })

      console.log('✅ 下拉菜单修复完成')
      return true
    } else {
      console.error('❌ 重新获取DOM元素失败')
      return false
    }
  }

  // 添加全局错误处理
  window.addEventListener('error', function (e) {
    componentLoader.showError('发生错误: ' + e.message)
  })
})

// 备用初始化方法 - 使用 window.onload 确保所有资源都已加载
window.addEventListener('load', function () {
  console.log('window load 事件触发')

  // 如果停车场切换器还没有初始化，再次尝试
  setTimeout(() => {
    if (!window.parkingSwitcher) {
      console.log('备用初始化：尝试创建停车场切换器')

      const domReady =
        document.getElementById('parkingListDropdown') &&
        document.getElementById('currentParkingName')
      const storageReady = typeof storage !== 'undefined' && storage.getParkingLots

      if (domReady && storageReady) {
        try {
          window.parkingSwitcher = new ParkingSwitcher()
          console.log('备用初始化：停车场切换器创建成功')
        } catch (error) {
          console.error('备用初始化失败:', error)
        }
      } else {
        console.log('备用初始化：条件不满足', { domReady, storageReady })
      }
    } else {
      console.log('停车场切换器已存在，无需备用初始化')
    }
  }, 1000)
})

// 工具函数：格式化时间
function formatDateTime(date) {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(new Date(date))
}

// 工具函数：格式化金额
function formatCurrency(amount, currency = '¥') {
  return `${currency}${amount.toFixed(2)}`
}

// 工具函数：数字动画
function animateNumber(element, target, duration = 1000) {
  const start = parseInt(element.textContent.replace(/[^0-9]/g, '') || 0)
  const startTime = performance.now()

  function update(currentTime) {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    const currentValue = Math.floor(start + (target - start) * progress)
    element.textContent = currentValue.toLocaleString()

    if (progress < 1) {
      requestAnimationFrame(update)
    } else {
      element.textContent = target.toLocaleString()
    }
  }

  requestAnimationFrame(update)
}
