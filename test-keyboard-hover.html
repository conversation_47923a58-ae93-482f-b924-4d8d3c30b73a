<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟键盘悬停效果测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* 测试用的辅助样式 */
        .test-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 20000;
        }
        
        .center-marker {
            position: fixed;
            top: 50%;
            left: 50%;
            width: 4px;
            height: 4px;
            background: red;
            transform: translate(-50%, -50%);
            z-index: 20001;
            border-radius: 50%;
        }
        
        .center-marker::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 1px dashed red;
            border-radius: 50%;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    
    <!-- 测试信息显示 -->
    <div class="test-info">
        <div>虚拟键盘悬停效果测试</div>
        <div>红点标记屏幕中心位置</div>
        <div>键盘应该始终保持居中，不会因悬停而移动</div>
    </div>
    
    <!-- 屏幕中心标记 -->
    <div class="center-marker"></div>
    
    <div class="flex items-center justify-center min-h-screen">
        <div class="text-center">
            <h1 class="text-3xl font-bold mb-8">虚拟键盘悬停测试</h1>
            <button id="showKeyboard" class="btn-primary">显示虚拟键盘</button>
            <p class="mt-4 text-gray-300">点击按钮显示键盘，然后将鼠标悬停在键盘上测试位置是否稳定</p>
        </div>
    </div>

    <!-- 虚拟键盘遮罩层和键盘容器 -->
    <div id="keyboardOverlay" class="keyboard-overlay hidden"></div>
    <div id="virtualKeyboard" class="virtual-keyboard glass-card hidden">
        <div class="keyboard-section">
            <div class="keyboard-row">
                <button class="key-btn" data-value="京">京</button>
                <button class="key-btn" data-value="津">津</button>
                <button class="key-btn" data-value="冀">冀</button>
                <button class="key-btn" data-value="晋">晋</button>
                <button class="key-btn" data-value="蒙">蒙</button>
                <button class="key-btn" data-value="辽">辽</button>
                <button class="key-btn" data-value="吉">吉</button>
                <button class="key-btn" data-value="黑">黑</button>
            </div>
            <div class="keyboard-row">
                <button class="key-btn" data-value="沪">沪</button>
                <button class="key-btn" data-value="苏">苏</button>
                <button class="key-btn" data-value="浙">浙</button>
                <button class="key-btn" data-value="皖">皖</button>
                <button class="key-btn" data-value="闽">闽</button>
                <button class="key-btn" data-value="赣">赣</button>
                <button class="key-btn" data-value="鲁">鲁</button>
                <button class="key-btn" data-value="豫">豫</button>
            </div>
        </div>

        <div class="keyboard-section">
            <div class="keyboard-row">
                <button class="key-btn" data-value="A">A</button>
                <button class="key-btn" data-value="B">B</button>
                <button class="key-btn" data-value="C">C</button>
                <button class="key-btn" data-value="D">D</button>
                <button class="key-btn" data-value="E">E</button>
                <button class="key-btn" data-value="F">F</button>
                <button class="key-btn" data-value="G">G</button>
                <button class="key-btn" data-value="H">H</button>
            </div>
            <div class="keyboard-row">
                <button class="key-btn" data-value="1">1</button>
                <button class="key-btn" data-value="2">2</button>
                <button class="key-btn" data-value="3">3</button>
                <button class="key-btn" data-value="4">4</button>
                <button class="key-btn" data-value="5">5</button>
                <button class="key-btn special" data-action="close">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // 简单的键盘显示/隐藏逻辑
        const showKeyboardBtn = document.getElementById('showKeyboard');
        const virtualKeyboard = document.getElementById('virtualKeyboard');
        const keyboardOverlay = document.getElementById('keyboardOverlay');

        showKeyboardBtn.addEventListener('click', () => {
            keyboardOverlay.classList.remove('hidden');
            virtualKeyboard.classList.remove('hidden');
        });

        keyboardOverlay.addEventListener('click', () => {
            keyboardOverlay.classList.add('hidden');
            virtualKeyboard.classList.add('hidden');
        });

        // 键盘按钮点击事件
        virtualKeyboard.addEventListener('click', (e) => {
            if (e.target.classList.contains('key-btn')) {
                const action = e.target.getAttribute('data-action');
                if (action === 'close') {
                    keyboardOverlay.classList.add('hidden');
                    virtualKeyboard.classList.add('hidden');
                }
            }
        });

        // 阻止键盘内部点击事件冒泡
        virtualKeyboard.addEventListener('click', e => {
            e.stopPropagation();
        });
    </script>
</body>
</html>
