/* 玻璃态效果 */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.36);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 卡片悬停效果 */
.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
}

/* 状态指示器 */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-active {
  background: #10b981;
  animation: pulse 2s infinite;
}

.status-inactive {
  background: #6b7280;
}

.status-warning {
  background: #f59e0b;
  animation: pulse 1.5s infinite;
}

/* 计数动画 */
.count-up {
  font-variant-numeric: tabular-nums;
}

/* 布局修复 - 确保主内容区域不被header遮挡 */
main {
  padding-top: 5rem !important; /* 增加顶部padding以避免被header遮挡 */
}

/* 大屏幕设备的布局优化 */
@media (min-width: 1024px) {
  main {
    margin-left: 16rem !important; /* lg:ml-64 = 16rem，确保不被sidebar遮挡 */
    padding-top: 5rem !important; /* 保持足够的顶部padding */
  }
}

/* 修复下拉菜单层级问题 */
.dropdown-menu {
  z-index: 9999 !important;
}

/* 确保停车场切换器下拉菜单不遮挡主内容 */
#parkingDropdown {
  z-index: 9999 !important;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .glass-card {
    margin: 0.5rem;
    padding: 1rem;
    border-radius: 1rem;
  }

  main {
    padding: 0.75rem !important;
    padding-top: 4.5rem !important; /* 移动端header较小，调整padding */
    margin-left: 0 !important; /* 移动端不需要左边距 */
  }

  /* 移动端导航优化 */
  header {
    padding: 0.5rem 1rem;
  }

  header h1 {
    font-size: 1.25rem;
  }

  /* 移动端按钮优化 */
  .btn-primary,
  .btn-secondary {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }

  /* 移动端表单优化 */
  .input-glass {
    padding: 0.625rem 0.875rem;
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 移动端车牌输入优化 */
  .license-plate-input .flex {
    flex-direction: column;
    space-y: 0.5rem;
  }

  .license-segment {
    width: 100%;
  }

  .license-segment select,
  .license-segment input {
    width: 100% !important;
    text-align: center;
  }

  /* 移动端表格优化 */
  table {
    font-size: 0.75rem;
  }

  /* 移动端网格优化 */
  .grid {
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .glass-card {
    margin: 0.75rem;
    padding: 1.25rem;
  }

  main {
    padding: 1rem !important;
    padding-top: 5rem !important; /* 平板端保持足够的顶部padding */
    margin-left: 0 !important; /* 平板端sidebar通常是隐藏的 */
  }

  /* 平板端优化 */
  .grid-cols-1 {
    grid-template-columns: 1fr;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .glass-card:hover {
    transform: none;
  }

  button,
  a {
    min-height: 44px; /* 苹果推荐的最小触摸目标 */
    min-width: 44px;
  }

  /* 增加触摸目标的间距 */
  .space-y-2 > * + * {
    margin-top: 0.75rem;
  }

  .space-y-4 > * + * {
    margin-top: 1.25rem;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .glass-card {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
  }

  .input-glass {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .glass-card {
    border-width: 2px;
  }

  .btn-primary,
  .btn-secondary {
    border-width: 2px;
  }

  .text-gray-300,
  .text-gray-400 {
    color: #fff !important;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .glass-card:hover {
    transform: none !important;
    transition: none !important;
  }
}

/* 加载动画 */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 表单样式 */
.input-glass {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: white;
  backdrop-filter: blur(10px);
}

.input-glass:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 车牌分段输入样式 */
.license-plate-input {
  position: relative;
}

.license-segment {
  flex-shrink: 0;
}

.license-segment select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23ffffff" d="m0 1 2 2 2-2z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 0.7rem center;
  background-size: 0.65rem auto;
  padding-right: 2rem;
}

.license-segment select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 下拉框选项样式 */
select option {
  background: #1f2937;
  color: white;
  padding: 0.5rem;
}

select option:hover,
select option:focus,
select option:checked {
  background: #3b82f6;
  color: white;
}

/* 确保下拉箭头在深色背景下可见 */
.license-segment select {
  background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23ffffff" d="m0 1 2 2 2-2z"/></svg>');
}

.license-segment input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 车牌显示区域样式 */
.license-display-area {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
  user-select: none;
}

.license-display-area:hover {
  border-color: rgba(59, 130, 246, 0.5);
  background: rgba(59, 130, 246, 0.1);
}

.license-display-area.active {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.2);
  color: white;
}

.license-display-area.filled {
  color: white;
  border-color: rgba(34, 197, 94, 0.5);
  background: rgba(34, 197, 94, 0.1);
}

.license-display-area.error {
  border-color: rgba(239, 68, 68, 0.5);
  background: rgba(239, 68, 68, 0.1);
}

/* 虚拟键盘样式 */
.virtual-keyboard {
  position: fixed;
  top: auto;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  width: 90vw;
  max-width: 600px;
  max-height: 70vh;
  overflow-y: auto;
  padding: 1rem;
  animation: slideUp 0.3s ease;
  border-radius: 1rem;
  /* 性能优化 */
  will-change: transform;
  backface-visibility: hidden;
  /* 防止抖动 */
  transform-style: preserve-3d;
}

/* 中等屏幕优化 */
@media (max-width: 1024px) {
  .virtual-keyboard {
    bottom: 1rem;
    max-height: 65vh;
  }
}

/* 小屏幕优化 */
@media (max-width: 768px) {
  .virtual-keyboard {
    bottom: 0;
    border-radius: 1rem 1rem 0 0;
    max-height: 60vh;
  }
}

/* 键盘可拖拽指示器 */
.keyboard-drag-handle {
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  margin: 0 auto 0.5rem;
  cursor: grab;
}

.keyboard-drag-handle:active {
  cursor: grabbing;
}

/* 修复虚拟键盘悬停效果 - 确保键盘位置不会因为glass-card:hover而改变 */
.virtual-keyboard:hover {
  transform: translateX(-50%) !important;
  /* 保持底部居中位置，不应用glass-card的hover效果 */
}

/* 虚拟键盘遮罩层 */
.keyboard-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.keyboard-section {
  margin-bottom: 0.75rem;
}

.keyboard-section:last-child {
  margin-bottom: 0;
}

/* 键盘预览区域 */
.keyboard-preview {
  background: rgba(59, 130, 246, 0.2);
  border: 2px solid rgba(59, 130, 246, 0.5);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  text-align: center;
}

.preview-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
}

.preview-content {
  font-size: 1.5rem;
  font-weight: 600;
  font-family: monospace;
  color: white;
  letter-spacing: 0.1em;
  min-height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-content.has-value {
  color: #22c55e;
  text-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

.keyboard-row {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.keyboard-row:last-child {
  margin-bottom: 0;
}

.key-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  min-width: 3rem;
  color: white;
  font-family: monospace;
  font-size: 1.25rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.key-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  /* 性能优化 */
  will-change: transform, box-shadow;
}

.key-btn:active {
  transform: translateY(0);
  background: rgba(59, 130, 246, 0.3);
  /* 防止连续点击时的抖动 */
  transition: all 0.1s ease;
}

.key-btn.special {
  background: rgba(107, 114, 128, 0.3);
  font-size: 1rem;
  min-width: 4rem;
}

.key-btn.primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  min-width: 5rem;
  font-size: 1rem;
}

.key-btn.primary:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* 移动端虚拟键盘优化 */
@media (max-width: 768px) {
  .virtual-keyboard {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: auto;
    transform: none;
    width: 100%;
    max-width: none;
    z-index: 10000;
    border-radius: 1rem 1rem 0 0;
    margin-top: 0;
    padding: 1rem 0.5rem;
    max-height: 60vh;
    overflow-y: auto;
    /* 移动端性能优化 */
    will-change: transform;
    backface-visibility: hidden;
    /* 防止iOS Safari的弹性滚动问题 */
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端预览区域优化 */
  .keyboard-preview {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .preview-content {
    font-size: 1.25rem;
  }
}

  .key-btn {
    min-width: 2.5rem;
    padding: 0.5rem;
    font-size: 1rem;
  }

  .key-btn.special {
    min-width: 3.5rem;
    font-size: 0.875rem;
  }

  .key-btn.primary {
    min-width: 4rem;
    font-size: 0.875rem;
  }

  .keyboard-row {
    gap: 0.25rem;
    margin-bottom: 0.25rem;
  }

  /* 移动端虚拟键盘悬停效果修复 */
  .virtual-keyboard:hover {
    transform: none !important;
    /* 移动端保持底部固定位置，不应用glass-card的hover效果 */
  }
}

/* 触摸设备键盘优化 */
@media (hover: none) and (pointer: coarse) {
  .key-btn {
    min-height: 44px;
    min-width: 44px;
  }
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  color: white;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}
