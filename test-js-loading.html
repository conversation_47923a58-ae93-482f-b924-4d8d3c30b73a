<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JavaScript加载测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background: #f0f0f0;
      }
      .test-section {
        background: white;
        padding: 20px;
        margin: 10px 0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .success {
        color: green;
      }
      .error {
        color: red;
      }
      .warning {
        color: orange;
      }
    </style>
  </head>
  <body>
    <h1>JavaScript文件加载测试</h1>

    <div class="test-section">
      <h2>测试结果</h2>
      <div id="testResults"></div>
    </div>

    <div class="test-section">
      <h2>控制台日志</h2>
      <p>请打开浏览器开发者工具查看控制台输出</p>
      <div
        id="consoleOutput"
        style="
          background: #f5f5f5;
          padding: 10px;
          border-radius: 4px;
          font-family: monospace;
          max-height: 200px;
          overflow-y: auto;
        "
      ></div>
    </div>

    <!-- 只加载一次JavaScript文件 -->
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/vehicle-entry.js"></script>

    <script>
      function runTests() {
        const results = document.getElementById('testResults')
        let html = ''

        // 测试1: 检查类是否正确定义
        html += '<h3>类定义检查</h3>'

        try {
          if (typeof ParkingStorage === 'function') {
            html += '<p class="success">✓ ParkingStorage 类定义正常</p>'
          } else {
            html += '<p class="error">✗ ParkingStorage 类未定义</p>'
          }
        } catch (e) {
          html += '<p class="error">✗ ParkingStorage 检查出错: ' + e.message + '</p>'
        }

        try {
          if (typeof ComponentLoader === 'function') {
            html += '<p class="success">✓ ComponentLoader 类定义正常</p>'
          } else {
            html += '<p class="error">✗ ComponentLoader 类未定义</p>'
          }
        } catch (e) {
          html += '<p class="error">✗ ComponentLoader 检查出错: ' + e.message + '</p>'
        }

        try {
          if (typeof VehicleEntry === 'function') {
            html += '<p class="success">✓ VehicleEntry 类定义正常</p>'
          } else {
            html += '<p class="error">✗ VehicleEntry 类未定义</p>'
          }
        } catch (e) {
          html += '<p class="error">✗ VehicleEntry 检查出错: ' + e.message + '</p>'
        }

        // 测试2: 检查全局实例
        html += '<h3>全局实例检查</h3>'

        try {
          if (typeof storage !== 'undefined') {
            html += '<p class="success">✓ storage 实例存在</p>'
          } else {
            html += '<p class="error">✗ storage 实例不存在</p>'
          }
        } catch (e) {
          html += '<p class="error">✗ storage 检查出错: ' + e.message + '</p>'
        }

        try {
          if (typeof componentLoader !== 'undefined') {
            html += '<p class="success">✓ componentLoader 实例存在</p>'
          } else {
            html += '<p class="error">✗ componentLoader 实例不存在</p>'
          }
        } catch (e) {
          html += '<p class="error">✗ componentLoader 检查出错: ' + e.message + '</p>'
        }

        // 测试3: 检查是否有重复声明
        html += '<h3>重复声明检查</h3>'
        html += '<p class="success">✓ 如果没有错误信息，说明没有重复声明问题</p>'

        results.innerHTML = html
      }

      // 捕获控制台输出
      const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn,
      }

      const consoleOutput = document.getElementById('consoleOutput')

      function addToConsole(type, message) {
        const div = document.createElement('div')
        div.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : 'black'
        div.textContent = `[${type.toUpperCase()}] ${message}`
        consoleOutput.appendChild(div)
        consoleOutput.scrollTop = consoleOutput.scrollHeight
      }

      console.log = function (...args) {
        originalConsole.log.apply(console, args)
        addToConsole('log', args.join(' '))
      }

      console.error = function (...args) {
        originalConsole.error.apply(console, args)
        addToConsole('error', args.join(' '))
      }

      console.warn = function (...args) {
        originalConsole.warn.apply(console, args)
        addToConsole('warn', args.join(' '))
      }

      // 捕获JavaScript错误
      window.addEventListener('error', function (e) {
        addToConsole('error', `JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`)
      })

      // 页面加载完成后运行测试
      window.addEventListener('load', function () {
        console.log('=== JavaScript加载测试开始 ===')
        console.log('ParkingStorage:', typeof ParkingStorage)
        console.log('ComponentLoader:', typeof ComponentLoader)
        console.log('VehicleEntry:', typeof VehicleEntry)
        console.log('storage:', typeof storage)
        console.log('componentLoader:', typeof componentLoader)
        console.log('=== JavaScript加载测试结束 ===')

        setTimeout(runTests, 500)
      })
    </script>
  </body>
</html>
